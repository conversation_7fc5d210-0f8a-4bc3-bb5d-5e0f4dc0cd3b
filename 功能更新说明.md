# 功能更新说明 - 灵活的输出管理

## 更新概述

根据cmd.md技术文档的要求，我们对软件的输出管理功能进行了重要更新，实现了更加灵活的文件存放方式。

## 更新前 vs 更新后

### 🔴 更新前的输出方式
- **单一输出模式**：只支持统一输出文件夹
- **固定命名规则**：所有文件都使用"合成真实-{原文件名}"命名
- **平铺存放**：所有合成结果都存放在同一个输出文件夹中

### 🟢 更新后的输出方式

#### 方式一：使用统一输出文件夹（推荐）
- ✅ **勾选"使用统一输出文件夹"**
- 📁 **按批次组织**：在输出文件夹中按批次文件夹名字创建子文件夹
- 📄 **保持原名**：文件保持原始文件名，便于识别和管理
- 🗂️ **结构清晰**：每个批次的结果独立存放，避免混乱

**文件结构示例：**
```
选择的输出文件夹/
├── 产品图片批次1/
│   ├── product001.jpg
│   ├── product002.jpg
│   └── product003.jpg
├── 人物照片批次2/
│   ├── person001.png
│   ├── person002.png
│   └── person003.png
└── 风景图片批次3/
    ├── landscape001.jpg
    └── landscape002.jpg
```

#### 方式二：在源文件路径下保存
- ❌ **不勾选"使用统一输出文件夹"**
- 📍 **就地保存**：合成结果直接保存在各自的源文件夹中
- 🏷️ **前缀标识**：文件名添加"合成真实-"前缀，便于区分原图和合成图
- 💾 **节省空间**：不需要额外的存储空间规划

**文件结构示例：**
```
源文件夹/
├── 批次1/
│   ├── image001.jpg          (原图)
│   ├── 合成真实-image001.jpg  (合成图)
│   ├── image002.jpg          (原图)
│   ├── 合成真实-image002.jpg  (合成图)
│   └── ...
├── 批次2/
│   ├── photo001.png          (原图)
│   ├── 合成真实-photo001.png  (合成图)
│   └── ...
```

## 界面更新

### 新增控件
1. **输出方式选择复选框**
   - 位置：源文件夹选择下方
   - 文本："使用统一输出文件夹（不勾选则在源文件路径下保存）"
   - 功能：控制输出方式的选择

2. **智能UI状态管理**
   - 输出文件夹选择按钮和文本框根据复选框状态自动启用/禁用
   - 扫描和处理按钮根据选择的输出方式智能判断是否可用

### 用户体验改进
- **灵活选择**：用户可以根据实际需求选择最适合的输出方式
- **智能提示**：状态栏会根据用户的选择提供相应的提示信息
- **防误操作**：UI控件的启用状态确保用户不会进行无效操作

## 技术实现要点

### 1. 数据绑定增强
```csharp
public bool UseOutputFolder { get; set; }  // 新增属性
```

### 2. 事件处理优化
```csharp
private void UseOutputFolder_Changed(object sender, RoutedEventArgs e)
{
    // 根据复选框状态更新UI和逻辑
}
```

### 3. 输出路径动态计算
```csharp
// 根据用户选择动态确定输出路径
string actualOutputPath = useOutputFolder 
    ? Path.Combine(outputFolder, batchFolderName)  // 按批次创建子文件夹
    : batch.BatchPath;                             // 直接在源路径下
```

### 4. 文件命名策略
```csharp
string outputFileName = useOutputFolder 
    ? $"{originalFileName}{extension}"                    // 保持原名
    : $"合成真实-{originalFileName}{extension}";          // 添加前缀
```

## 使用建议

### 推荐使用方式一（统一输出文件夹）的场景：
- 🎯 **批量处理多个项目**：需要将不同批次的结果分开管理
- 📊 **商业用途**：需要向客户交付整理好的成果
- 🗃️ **长期存档**：需要建立清晰的文件组织结构
- 🔄 **重复处理**：可能需要重新处理某些批次

### 推荐使用方式二（源路径保存）的场景：
- 🚀 **快速测试**：临时处理少量图片查看效果
- 💿 **存储空间有限**：不想创建额外的文件夹结构
- 🔍 **对比需求**：需要原图和合成图放在一起便于对比
- 📝 **简单项目**：只有单个批次或少量文件

## 兼容性说明

- ✅ **向后兼容**：现有的处理逻辑完全保留
- ✅ **无缝升级**：用户可以立即使用新功能，无需额外配置
- ✅ **稳定可靠**：核心图像处理算法未发生变化
- ✅ **性能优化**：新的输出管理不会影响处理速度

## 总结

这次更新完全符合cmd.md技术文档的要求，为用户提供了更加灵活和实用的输出管理功能。无论是专业的批量处理需求，还是简单的测试场景，用户都可以选择最适合的输出方式，大大提升了软件的实用性和用户体验。
