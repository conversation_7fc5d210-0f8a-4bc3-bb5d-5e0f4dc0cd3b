using System;
using OpenCvSharp;

namespace RealPicComposer
{
    /// <summary>
    /// 坐标转换测试工具
    /// </summary>
    public class CoordinateTest
    {
        public static void TestCoordinateMapping()
        {
            Console.WriteLine("=== 坐标转换测试 ===");
            
            // 模拟一个背景图尺寸
            int backgroundWidth = 2480;  // 假设背景图宽度
            int backgroundHeight = 3508; // 假设背景图高度
            
            Console.WriteLine($"背景图尺寸: {backgroundWidth} x {backgroundHeight}");
            
            // 模拟用户设置的四个角点（应该在背景图的中央区域）
            var destinationPoints = new Point2f[]
            {
                new Point2f(620, 877),   // 左上角 (大约1/4位置)
                new Point2f(1860, 877),  // 右上角 (大约3/4位置)
                new Point2f(1860, 2631), // 右下角
                new Point2f(620, 2631)   // 左下角
            };
            
            Console.WriteLine("目标区域四角点:");
            string[] pointNames = { "左上角", "右上角", "右下角", "左下角" };
            for (int i = 0; i < destinationPoints.Length; i++)
            {
                var point = destinationPoints[i];
                var percentX = (point.X / backgroundWidth) * 100;
                var percentY = (point.Y / backgroundHeight) * 100;
                Console.WriteLine($"  {pointNames[i]}: ({point.X}, {point.Y}) - 相对位置: ({percentX:F1}%, {percentY:F1}%)");
            }
            
            // 模拟内容图尺寸
            int contentWidth = 800;
            int contentHeight = 600;
            Console.WriteLine($"\n内容图尺寸: {contentWidth} x {contentHeight}");
            
            // 计算目标区域的尺寸
            var targetWidth = destinationPoints[1].X - destinationPoints[0].X;
            var targetHeight = destinationPoints[2].Y - destinationPoints[0].Y;
            Console.WriteLine($"目标区域尺寸: {targetWidth} x {targetHeight}");
            
            // 计算缩放比例
            var scaleX = targetWidth / contentWidth;
            var scaleY = targetHeight / contentHeight;
            Console.WriteLine($"缩放比例: X={scaleX:F2}, Y={scaleY:F2}");
            
            // 检查是否为矩形区域
            bool isRectangular = Math.Abs(destinationPoints[0].Y - destinationPoints[1].Y) < 1 &&
                               Math.Abs(destinationPoints[2].Y - destinationPoints[3].Y) < 1 &&
                               Math.Abs(destinationPoints[0].X - destinationPoints[3].X) < 1 &&
                               Math.Abs(destinationPoints[1].X - destinationPoints[2].X) < 1;
            
            Console.WriteLine($"是否为矩形区域: {(isRectangular ? "是" : "否")}");
            
            if (!isRectangular)
            {
                Console.WriteLine("警告: 目标区域不是标准矩形，可能会产生透视变形效果");
            }
        }
        
        public static void ValidatePoints(Point2f[] points, int imageWidth, int imageHeight)
        {
            Console.WriteLine("\n=== 坐标验证 ===");
            Console.WriteLine($"图像尺寸: {imageWidth} x {imageHeight}");
            
            bool allValid = true;
            string[] pointNames = { "左上角", "右上角", "右下角", "左下角" };
            
            for (int i = 0; i < points.Length; i++)
            {
                var point = points[i];
                bool isValid = point.X >= 0 && point.X < imageWidth && 
                              point.Y >= 0 && point.Y < imageHeight;
                
                Console.WriteLine($"{pointNames[i]}: ({point.X}, {point.Y}) - {(isValid ? "有效" : "无效")}");
                
                if (!isValid)
                {
                    allValid = false;
                    if (point.X < 0) Console.WriteLine($"  错误: X坐标 {point.X} 小于0");
                    if (point.X >= imageWidth) Console.WriteLine($"  错误: X坐标 {point.X} 超出图像宽度 {imageWidth}");
                    if (point.Y < 0) Console.WriteLine($"  错误: Y坐标 {point.Y} 小于0");
                    if (point.Y >= imageHeight) Console.WriteLine($"  错误: Y坐标 {point.Y} 超出图像高度 {imageHeight}");
                }
            }
            
            Console.WriteLine($"所有坐标是否有效: {(allValid ? "是" : "否")}");
        }
    }
}
