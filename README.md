# Real Pic Composer - 图片批量合成软件 v2.3

一款Windows桌面应用程序，支持将一批内容图片自动、批量地合成到指定的背景图上，具有交互式四角定位功能。

## 功能特点

- **用户友好的图形界面**：直观的操作界面，引导用户完成所有操作
- **灵活的输入处理**：自动遍历嵌套子文件夹，识别包含图片的文件夹作为处理批次
- **交互式四角定位**：通过鼠标点击精确指定内容图在背景图上的位置
- **按批次指定背景**：为每个批次独立选择不同的背景图
- **真实感合成**：使用透视变换技术，生成具有真实感的合成效果
- **进度反馈**：实时显示处理进度，避免程序假死
- **自然排序**：确保文件按正确的数字顺序处理

## 技术架构

- **开发语言**: C#
- **GUI框架**: WPF (Windows Presentation Foundation)
- **图像处理库**: OpenCvSharp4

## 使用方法

### 第一步：选择源与输出
1. 点击"选择源文件夹"按钮，选择包含图片的文件夹
2. 选择输出方式：
   - **勾选"使用统一输出文件夹"**：所有合成结果保存到指定的输出文件夹中，按批次文件夹名字创建子文件夹
   - **不勾选**：合成结果直接保存在源文件路径下，文件名添加"合成真实-"前缀
3. 如果勾选了使用输出文件夹，点击"选择输出文件夹"按钮选择保存位置

### 第二步：扫描与批次配置
1. 点击"扫描文件夹"按钮，软件会自动识别所有包含图片的文件夹作为处理批次
2. 在批次列表中，为每个批次进行配置：
   - 勾选需要处理的批次
   - 点击"选择背景"为批次选择背景图
   - 点击"设置定位点"进入交互式定位模式

### 第三步：交互式定位
1. 在定位窗口中，依次点击背景图上的四个角点：
   - 左上角（红色标记）
   - 右上角（蓝色标记）
   - 右下角（绿色标记）
   - 左下角（橙色标记）
2. 设置完成后点击"确定"保存定位点

### 第四步：开始处理
1. 确认所有批次都已正确配置
2. 点击"开始处理"按钮
3. 观察进度条和状态信息，等待处理完成

## 支持的图片格式

- JPEG (.jpg, .jpeg)
- PNG (.png)
- BMP (.bmp)
- TIFF (.tiff, .tif)
- GIF (.gif)

## 输出文件管理

软件提供两种输出方式：

### 方式一：使用统一输出文件夹（推荐）
- 勾选"使用统一输出文件夹"选项
- 选择一个输出文件夹
- 合成结果按批次文件夹名字创建子文件夹存放
- 文件保持原始文件名

**文件结构示例：**
```
输出文件夹/
├── 批次1文件夹名/
│   ├── image001.jpg
│   ├── image002.jpg
│   └── ...
├── 批次2文件夹名/
│   ├── photo001.png
│   ├── photo002.png
│   └── ...
```

### 方式二：在源文件路径下保存
- 不勾选"使用统一输出文件夹"选项
- 合成结果直接保存在各自的源文件夹中
- 文件名添加"合成真实-"前缀

**命名规则：**
```
合成真实-{原文件名}.{扩展名}
```

**示例：** `image001.jpg` → `合成真实-image001.jpg`

## 系统要求

- Windows 10 或更高版本
- .NET 6.0 Runtime
- 足够的磁盘空间用于存储合成结果

## 构建和运行

### 开发环境要求
- Visual Studio 2022 或 Visual Studio Code
- .NET 6.0 SDK

### 构建项目
```bash
dotnet build RealPicComposer.csproj
```

### 运行项目
```bash
dotnet run --project RealPicComposer.csproj
```

## 注意事项

1. **文件夹结构**：软件会自动遍历所有子文件夹，每个包含图片的文件夹都会被识别为一个批次
2. **定位精度**：四角定位点的精确度直接影响合成效果，建议仔细选择
3. **图片质量**：建议使用高质量的背景图和内容图以获得最佳合成效果
4. **处理时间**：处理时间取决于图片数量、尺寸和计算机性能

## 版本历史

### v2.3 (2025-07-31)
- 新增交互式四角定位功能
- 改进用户界面设计
- 优化批次管理和进度反馈
- 增强错误处理和用户提示

## 许可证

本项目仅供学习和研究使用。

## 技术支持

如有问题或建议，请通过以下方式联系：
- 查看项目文档
- 提交Issue报告问题
