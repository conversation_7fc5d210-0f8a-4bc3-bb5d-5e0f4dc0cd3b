<Window x:Class="RealPicComposer.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Real Pic Composer - 图片批量合成软件 v2.3" 
        Height="700" Width="1200"
        MinHeight="600" MinWidth="800"
        WindowStartupLocation="CenterScreen">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#2C3E50" Padding="10">
            <TextBlock Text="图片批量合成软件" 
                       FontSize="18" 
                       FontWeight="Bold" 
                       Foreground="White" 
                       HorizontalAlignment="Center"/>
        </Border>

        <!-- 文件夹选择区域 -->
        <GroupBox Grid.Row="1" Header="第一步：选择源与输出" Margin="10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 源文件夹选择 -->
                <TextBlock Grid.Row="0" Grid.Column="0" Text="源文件夹:" Width="80"/>
                <TextBox Grid.Row="0" Grid.Column="1" x:Name="SourceFolderTextBox" IsReadOnly="True"/>
                <Button Grid.Row="0" Grid.Column="2" Content="选择源文件夹" Click="SelectSourceFolder_Click"/>

                <!-- 输出方式选择 -->
                <CheckBox Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="3"
                          x:Name="UseOutputFolderCheckBox"
                          Content="使用统一输出文件夹（不勾选则在源文件路径下保存）"
                          Margin="5"
                          Checked="UseOutputFolder_Changed"
                          Unchecked="UseOutputFolder_Changed"/>

                <!-- 输出文件夹选择 -->
                <TextBlock Grid.Row="2" Grid.Column="0" Text="输出文件夹:" Width="80"/>
                <TextBox Grid.Row="2" Grid.Column="1" x:Name="OutputFolderTextBox" IsReadOnly="True"/>
                <Button Grid.Row="2" Grid.Column="2" Content="选择输出文件夹"
                        x:Name="SelectOutputFolderButton"
                        Click="SelectOutputFolder_Click"/>
            </Grid>
        </GroupBox>

        <!-- 批次配置区域 -->
        <GroupBox Grid.Row="2" Header="第二步：扫描与批次配置" Margin="10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 扫描按钮 -->
                <StackPanel Orientation="Horizontal">
                    <Button Grid.Row="0" Content="扫描文件夹"
                            x:Name="ScanButton"
                            Click="ScanFolder_Click"
                            HorizontalAlignment="Left"
                            IsEnabled="False"
                            Margin="0,0,10,0"/>

                    <!-- 统一背景设置选项 -->
                    <CheckBox x:Name="UseUnifiedBackgroundCheckBox"
                              Content="所有批次使用统一背景和定位设置"
                              VerticalAlignment="Center"
                              Margin="10,0,10,0"
                              Checked="UseUnifiedBackground_Checked"
                              Unchecked="UseUnifiedBackground_Unchecked"/>

                    <Button Content="设置统一背景"
                            x:Name="SetUnifiedBackgroundButton"
                            Click="SetUnifiedBackground_Click"
                            IsEnabled="False"
                            Margin="10,0,0,0"/>
                </StackPanel>

                <!-- 批次列表 -->
                <DataGrid Grid.Row="1" 
                          x:Name="BatchDataGrid" 
                          ItemsSource="{Binding BatchItems}"
                          Margin="0,10,0,0">
                    <DataGrid.Columns>
                        <DataGridCheckBoxColumn Header="选择" Binding="{Binding IsSelected}" Width="60"/>
                        <DataGridTextColumn Header="批次路径" Binding="{Binding BatchPath}" Width="300" IsReadOnly="True"/>
                        <DataGridTextColumn Header="图片数量" Binding="{Binding ImageCount}" Width="80" IsReadOnly="True"/>
                        <DataGridTextColumn Header="背景图路径" Binding="{Binding BackgroundPath}" Width="250" IsReadOnly="True"/>
                        <DataGridTemplateColumn Header="操作" Width="200">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="选择背景" 
                                                Click="SelectBackground_Click" 
                                                Tag="{Binding}"
                                                Width="80"
                                                Margin="2"/>
                                        <Button Content="设置定位点" 
                                                Click="SetPosition_Click" 
                                                Tag="{Binding}"
                                                IsEnabled="{Binding CanSetPosition}"
                                                Width="80"
                                                Margin="2"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTextColumn Header="定位状态" Binding="{Binding PositionStatus}" Width="80" IsReadOnly="True"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </GroupBox>

        <!-- 处理控制区域 -->
        <GroupBox Grid.Row="3" Header="第三步：处理与反馈" Margin="10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 开始处理按钮 -->
                <Button Grid.Row="0" Grid.Column="0" 
                        Content="开始处理" 
                        x:Name="StartProcessButton"
                        Click="StartProcess_Click" 
                        IsEnabled="False"
                        Width="100"/>

                <!-- 进度条 -->
                <ProgressBar Grid.Row="0" Grid.Column="1" 
                             x:Name="ProgressBar" 
                             Height="25" 
                             Margin="10,0"/>

                <!-- 进度文本 -->
                <TextBlock Grid.Row="0" Grid.Column="2" 
                           x:Name="ProgressText" 
                           Text="准备就绪" 
                           Width="150"/>

                <!-- 状态信息 -->
                <TextBlock Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="3" 
                           x:Name="StatusText" 
                           Text="请选择源文件夹和输出文件夹开始操作" 
                           Margin="0,5,0,0"/>
            </Grid>
        </GroupBox>

        <!-- 底部信息栏 -->
        <Border Grid.Row="4" Background="#34495E" Padding="10">
            <TextBlock Text="Real Pic Composer v2.3 - 支持交互式四角定位的图片批量合成软件" 
                       Foreground="White" 
                       HorizontalAlignment="Center"/>
        </Border>
    </Grid>
</Window>
