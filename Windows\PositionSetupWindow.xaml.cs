using System;
using System.Collections.Generic;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using OpenCvSharp;
using RealPicComposer.Models;

namespace RealPicComposer.Windows
{
    /// <summary>
    /// PositionSetupWindow.xaml 的交互逻辑
    /// </summary>
    public partial class PositionSetupWindow : System.Windows.Window
    {
        private readonly BatchItem _batchItem;
        private readonly List<System.Windows.Point> _selectedPoints;
        private readonly List<Ellipse> _markers;
        private BitmapImage? _backgroundBitmap;
        private int _currentPointIndex;

        // 区域选择模式的变量
        private bool _isAreaSelectionMode = true;  // 默认使用区域选择模式
        private bool _isDragging = false;
        private System.Windows.Point _dragStartPoint;
        private Rectangle? _selectionRectangle;

        private static readonly string[] PointNames = { "左上角", "右上角", "右下角", "左下角" };
        private static readonly Brush[] PointColors =
        {
            Brushes.Red,    // 左上角 - 红色
            Brushes.Blue,   // 右上角 - 蓝色
            Brushes.Green,  // 右下角 - 绿色
            Brushes.Orange  // 左下角 - 橙色
        };

        public PositionSetupWindow(BatchItem batchItem)
        {
            InitializeComponent();

            _batchItem = batchItem ?? throw new ArgumentNullException(nameof(batchItem));
            _selectedPoints = new List<System.Windows.Point>();
            _markers = new List<Ellipse>();
            _currentPointIndex = 0;

            LoadBackgroundImage();
            LoadExistingPoints();
            UpdateModeInstructions();
        }

        private void LoadBackgroundImage()
        {
            try
            {
                if (!File.Exists(_batchItem.BackgroundPath))
                {
                    MessageBox.Show("背景图文件不存在", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    DialogResult = false;
                    return;
                }

                _backgroundBitmap = new BitmapImage();
                _backgroundBitmap.BeginInit();
                _backgroundBitmap.UriSource = new Uri(_batchItem.BackgroundPath);
                _backgroundBitmap.CacheOption = BitmapCacheOption.OnLoad;
                _backgroundBitmap.EndInit();

                BackgroundImage.Source = _backgroundBitmap;
                
                // 如果已有定位点，加载它们
                if (_batchItem.DestinationPoints != null && _batchItem.DestinationPoints.Length == 4)
                {
                    LoadExistingPoints();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载背景图失败：{ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                DialogResult = false;
            }
        }

        private void LoadExistingPoints()
        {
            if (_batchItem.DestinationPoints == null) return;

            _selectedPoints.Clear();
            foreach (var point in _batchItem.DestinationPoints)
            {
                _selectedPoints.Add(new System.Windows.Point(point.X, point.Y));
            }

            _currentPointIndex = 4;
            RedrawMarkers();
            UpdatePointTexts();
            UpdateInstruction();
            ConfirmButton.IsEnabled = true;
        }

        private void Canvas_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (_isAreaSelectionMode)
            {
                _isDragging = true;
                _dragStartPoint = e.GetPosition(MarkersCanvas);
                MarkersCanvas.CaptureMouse();

                // 清除之前的选择
                ClearSelection();
            }
        }

        private void Canvas_MouseMove(object sender, MouseEventArgs e)
        {
            if (_isAreaSelectionMode && _isDragging)
            {
                var currentPoint = e.GetPosition(MarkersCanvas);
                UpdateSelectionRectangle(_dragStartPoint, currentPoint);
            }
        }

        private void Canvas_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (_isAreaSelectionMode && _isDragging)
            {
                _isDragging = false;
                MarkersCanvas.ReleaseMouseCapture();

                var endPoint = e.GetPosition(MarkersCanvas);
                CreateRectangularSelection(_dragStartPoint, endPoint);
            }
            else if (!_isAreaSelectionMode)
            {
                // 原有的点击模式
                if (_currentPointIndex >= 4) return;

                var canvasPosition = e.GetPosition(MarkersCanvas);
                var imagePosition = ConvertCanvasToImageCoordinates(canvasPosition);

                if (imagePosition.HasValue)
                {
                    _selectedPoints.Add(imagePosition.Value);
                    AddMarker(canvasPosition, _currentPointIndex);
                    UpdatePointText(_currentPointIndex, imagePosition.Value);

                    _currentPointIndex++;
                    UpdateInstruction();

                    if (_currentPointIndex >= 4)
                    {
                        ConfirmButton.IsEnabled = true;
                        InstructionText.Text = "四个角点已设置完成，请确认或重新设置";
                    }
                }
            }
        }

        private System.Windows.Point? ConvertCanvasToImageCoordinates(System.Windows.Point canvasPoint)
        {
            if (_backgroundBitmap == null || BackgroundImage.ActualWidth == 0 || BackgroundImage.ActualHeight == 0)
                return null;

            // 获取Image控件的实际显示区域
            var imageRect = GetImageDisplayRect();
            
            // 检查点击是否在图片区域内
            if (!imageRect.Contains(canvasPoint))
                return null;

            // 转换为相对于图片显示区域的坐标
            var relativeX = (canvasPoint.X - imageRect.X) / imageRect.Width;
            var relativeY = (canvasPoint.Y - imageRect.Y) / imageRect.Height;

            // 转换为图片原始像素坐标
            var imageX = relativeX * _backgroundBitmap.PixelWidth;
            var imageY = relativeY * _backgroundBitmap.PixelHeight;

            return new System.Windows.Point(imageX, imageY);
        }

        private System.Windows.Rect GetImageDisplayRect()
        {
            if (_backgroundBitmap == null) return new System.Windows.Rect();

            var canvasWidth = MarkersCanvas.ActualWidth;
            var canvasHeight = MarkersCanvas.ActualHeight;
            var imageAspectRatio = (double)_backgroundBitmap.PixelWidth / _backgroundBitmap.PixelHeight;
            var canvasAspectRatio = canvasWidth / canvasHeight;

            double displayWidth, displayHeight, offsetX, offsetY;

            if (imageAspectRatio > canvasAspectRatio)
            {
                // 图片更宽，以宽度为准
                displayWidth = canvasWidth;
                displayHeight = canvasWidth / imageAspectRatio;
                offsetX = 0;
                offsetY = (canvasHeight - displayHeight) / 2;
            }
            else
            {
                // 图片更高，以高度为准
                displayHeight = canvasHeight;
                displayWidth = canvasHeight * imageAspectRatio;
                offsetX = (canvasWidth - displayWidth) / 2;
                offsetY = 0;
            }

            return new System.Windows.Rect(offsetX, offsetY, displayWidth, displayHeight);
        }

        private void AddMarker(System.Windows.Point canvasPosition, int pointIndex)
        {
            var marker = new Ellipse
            {
                Width = 20,
                Height = 20,
                Fill = PointColors[pointIndex],
                Stroke = Brushes.White,
                StrokeThickness = 2
            };

            Canvas.SetLeft(marker, canvasPosition.X - 10);
            Canvas.SetTop(marker, canvasPosition.Y - 10);

            MarkersCanvas.Children.Add(marker);
            _markers.Add(marker);

            // 添加编号文本
            var numberText = new TextBlock
            {
                Text = (pointIndex + 1).ToString(),
                Foreground = Brushes.White,
                FontWeight = FontWeights.Bold,
                FontSize = 12,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            Canvas.SetLeft(numberText, canvasPosition.X - 6);
            Canvas.SetTop(numberText, canvasPosition.Y - 8);

            MarkersCanvas.Children.Add(numberText);
        }

        private void RedrawMarkers()
        {
            MarkersCanvas.Children.Clear();
            _markers.Clear();

            for (int i = 0; i < _selectedPoints.Count; i++)
            {
                var imagePoint = _selectedPoints[i];
                var canvasPoint = ConvertImageToCanvasCoordinates(imagePoint);
                if (canvasPoint.HasValue)
                {
                    AddMarker(canvasPoint.Value, i);
                }
            }
        }

        private System.Windows.Point? ConvertImageToCanvasCoordinates(System.Windows.Point imagePoint)
        {
            if (_backgroundBitmap == null) return null;

            var imageRect = GetImageDisplayRect();
            
            var relativeX = imagePoint.X / _backgroundBitmap.PixelWidth;
            var relativeY = imagePoint.Y / _backgroundBitmap.PixelHeight;

            var canvasX = imageRect.X + relativeX * imageRect.Width;
            var canvasY = imageRect.Y + relativeY * imageRect.Height;

            return new System.Windows.Point(canvasX, canvasY);
        }

        private void UpdatePointText(int index, System.Windows.Point point)
        {
            var text = $"({point.X:F0}, {point.Y:F0})";
            
            switch (index)
            {
                case 0: Point1Text.Text = text; break;
                case 1: Point2Text.Text = text; break;
                case 2: Point3Text.Text = text; break;
                case 3: Point4Text.Text = text; break;
            }
        }

        private void UpdatePointTexts()
        {
            for (int i = 0; i < _selectedPoints.Count; i++)
            {
                UpdatePointText(i, _selectedPoints[i]);
            }
        }

        private void UpdateInstruction()
        {
            if (_currentPointIndex < 4)
            {
                InstructionText.Text = $"请点击{PointNames[_currentPointIndex]} ({_currentPointIndex + 1}/4)";
            }
        }

        private void UpdateModeInstructions()
        {
            if (_isAreaSelectionMode)
            {
                InstructionText.Text = "请拖拽选择内容放置的矩形区域";
            }
            else
            {
                UpdateInstruction();
            }
        }

        private void ClearSelection()
        {
            _selectedPoints.Clear();
            _markers.Clear();
            MarkersCanvas.Children.Clear();
            _currentPointIndex = 0;

            // 清除选择矩形
            if (_selectionRectangle != null)
            {
                MarkersCanvas.Children.Remove(_selectionRectangle);
                _selectionRectangle = null;
            }

            Point1Text.Text = "未设置";
            Point2Text.Text = "未设置";
            Point3Text.Text = "未设置";
            Point4Text.Text = "未设置";

            ConfirmButton.IsEnabled = false;
        }

        private void UpdateSelectionRectangle(System.Windows.Point startPoint, System.Windows.Point currentPoint)
        {
            // 移除之前的选择矩形
            if (_selectionRectangle != null)
            {
                MarkersCanvas.Children.Remove(_selectionRectangle);
            }

            // 计算矩形的位置和大小
            var left = Math.Min(startPoint.X, currentPoint.X);
            var top = Math.Min(startPoint.Y, currentPoint.Y);
            var width = Math.Abs(currentPoint.X - startPoint.X);
            var height = Math.Abs(currentPoint.Y - startPoint.Y);

            // 创建新的选择矩形
            _selectionRectangle = new Rectangle
            {
                Width = width,
                Height = height,
                Stroke = Brushes.Red,
                StrokeThickness = 2,
                StrokeDashArray = new DoubleCollection { 5, 3 },
                Fill = new SolidColorBrush(Color.FromArgb(30, 255, 0, 0))
            };

            Canvas.SetLeft(_selectionRectangle, left);
            Canvas.SetTop(_selectionRectangle, top);
            MarkersCanvas.Children.Add(_selectionRectangle);
        }

        private void CreateRectangularSelection(System.Windows.Point startPoint, System.Windows.Point endPoint)
        {
            // 确保选择区域有最小尺寸
            var minSize = 20;
            if (Math.Abs(endPoint.X - startPoint.X) < minSize || Math.Abs(endPoint.Y - startPoint.Y) < minSize)
            {
                InstructionText.Text = "选择区域太小，请重新选择";
                ClearSelection();
                return;
            }

            // 计算矩形的四个角点（Canvas坐标）
            var left = Math.Min(startPoint.X, endPoint.X);
            var top = Math.Min(startPoint.Y, endPoint.Y);
            var right = Math.Max(startPoint.X, endPoint.X);
            var bottom = Math.Max(startPoint.Y, endPoint.Y);

            var canvasPoints = new System.Windows.Point[]
            {
                new System.Windows.Point(left, top),      // 左上角
                new System.Windows.Point(right, top),     // 右上角
                new System.Windows.Point(right, bottom),  // 右下角
                new System.Windows.Point(left, bottom)    // 左下角
            };

            // 转换为图像坐标并验证
            _selectedPoints.Clear();
            bool allPointsValid = true;

            for (int i = 0; i < canvasPoints.Length; i++)
            {
                var imagePoint = ConvertCanvasToImageCoordinates(canvasPoints[i]);
                if (imagePoint.HasValue)
                {
                    _selectedPoints.Add(imagePoint.Value);
                }
                else
                {
                    allPointsValid = false;
                    break;
                }
            }

            if (!allPointsValid || _selectedPoints.Count != 4)
            {
                InstructionText.Text = "选择区域超出图像范围，请重新选择";
                ClearSelection();
                return;
            }

            // 添加可视化标记
            _markers.Clear();
            MarkersCanvas.Children.Clear();

            for (int i = 0; i < canvasPoints.Length; i++)
            {
                AddMarker(canvasPoints[i], i);
                UpdatePointText(i, _selectedPoints[i]);
            }

            // 重新添加选择矩形
            UpdateSelectionRectangle(startPoint, endPoint);

            _currentPointIndex = 4;
            ConfirmButton.IsEnabled = true;
            InstructionText.Text = "区域选择完成，程序将自动保持图片比例进行合成";
        }

        private void AreaMode_Click(object sender, RoutedEventArgs e)
        {
            _isAreaSelectionMode = true;
            UpdateModeButtons();
            ClearSelection();
            UpdateModeInstructions();
        }

        private void PointMode_Click(object sender, RoutedEventArgs e)
        {
            _isAreaSelectionMode = false;
            UpdateModeButtons();
            ClearSelection();
            UpdateModeInstructions();
        }

        private void UpdateModeButtons()
        {
            if (_isAreaSelectionMode)
            {
                AreaModeButton.Background = new SolidColorBrush(Color.FromRgb(39, 174, 96));  // 绿色 - 激活
                PointModeButton.Background = new SolidColorBrush(Color.FromRgb(149, 165, 166)); // 灰色 - 非激活
            }
            else
            {
                AreaModeButton.Background = new SolidColorBrush(Color.FromRgb(149, 165, 166)); // 灰色 - 非激活
                PointModeButton.Background = new SolidColorBrush(Color.FromRgb(39, 174, 96));  // 绿色 - 激活
            }
        }

        private void Reset_Click(object sender, RoutedEventArgs e)
        {
            _selectedPoints.Clear();
            _markers.Clear();
            MarkersCanvas.Children.Clear();
            _currentPointIndex = 0;

            Point1Text.Text = "未设置";
            Point2Text.Text = "未设置";
            Point3Text.Text = "未设置";
            Point4Text.Text = "未设置";

            UpdateInstruction();
            ConfirmButton.IsEnabled = false;
        }

        private void Confirm_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedPoints.Count != 4)
            {
                MessageBox.Show("请设置完整的四个角点", "提示",
                    MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            // 验证所有点是否在图像范围内
            if (_backgroundBitmap != null)
            {
                for (int i = 0; i < _selectedPoints.Count; i++)
                {
                    var point = _selectedPoints[i];
                    if (point.X < 0 || point.X >= _backgroundBitmap.PixelWidth ||
                        point.Y < 0 || point.Y >= _backgroundBitmap.PixelHeight)
                    {
                        MessageBox.Show($"第{i+1}个角点 ({point.X:F0}, {point.Y:F0}) 超出图像范围 ({_backgroundBitmap.PixelWidth}x{_backgroundBitmap.PixelHeight})",
                            "坐标错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                }
            }

            // 转换为OpenCV的Point2f数组
            var points = new Point2f[4];
            for (int i = 0; i < 4; i++)
            {
                points[i] = new Point2f((float)_selectedPoints[i].X, (float)_selectedPoints[i].Y);
            }

            // 输出调试信息
            System.Diagnostics.Debug.WriteLine("设置的定位点坐标:");
            for (int i = 0; i < points.Length; i++)
            {
                System.Diagnostics.Debug.WriteLine($"  {PointNames[i]}: ({points[i].X}, {points[i].Y})");
            }

            _batchItem.DestinationPoints = points;
            DialogResult = true;
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
        }

        private void Window_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            // 窗口大小改变时重新绘制标记点
            if (_selectedPoints.Count > 0)
            {
                Dispatcher.BeginInvoke(new Action(() => RedrawMarkers()));
            }
        }
    }
}
