﻿#pragma checksum "..\..\..\..\Windows\PositionSetupWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "B8093738B7F9131205A15AE9D9D7BAB1EDEC66EB"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace RealPicComposer.Windows {
    
    
    /// <summary>
    /// PositionSetupWindow
    /// </summary>
    public partial class PositionSetupWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 33 "..\..\..\..\Windows\PositionSetupWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InstructionText;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\..\Windows\PositionSetupWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AreaModeButton;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\..\Windows\PositionSetupWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PointModeButton;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\..\Windows\PositionSetupWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image BackgroundImage;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\Windows\PositionSetupWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas MarkersCanvas;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\Windows\PositionSetupWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock Point1Text;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\Windows\PositionSetupWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock Point2Text;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\..\Windows\PositionSetupWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock Point3Text;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\Windows\PositionSetupWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock Point4Text;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\Windows\PositionSetupWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetButton;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\..\Windows\PositionSetupWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ConfirmButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.17.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/RealPicComposer;V2.3.0.0;component/windows/positionsetupwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Windows\PositionSetupWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.17.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 9 "..\..\..\..\Windows\PositionSetupWindow.xaml"
            ((RealPicComposer.Windows.PositionSetupWindow)(target)).SizeChanged += new System.Windows.SizeChangedEventHandler(this.Window_SizeChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.InstructionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.AreaModeButton = ((System.Windows.Controls.Button)(target));
            
            #line 44 "..\..\..\..\Windows\PositionSetupWindow.xaml"
            this.AreaModeButton.Click += new System.Windows.RoutedEventHandler(this.AreaMode_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.PointModeButton = ((System.Windows.Controls.Button)(target));
            
            #line 52 "..\..\..\..\Windows\PositionSetupWindow.xaml"
            this.PointModeButton.Click += new System.Windows.RoutedEventHandler(this.PointMode_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.BackgroundImage = ((System.Windows.Controls.Image)(target));
            return;
            case 6:
            this.MarkersCanvas = ((System.Windows.Controls.Canvas)(target));
            
            #line 74 "..\..\..\..\Windows\PositionSetupWindow.xaml"
            this.MarkersCanvas.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Canvas_MouseLeftButtonDown);
            
            #line default
            #line hidden
            
            #line 75 "..\..\..\..\Windows\PositionSetupWindow.xaml"
            this.MarkersCanvas.MouseMove += new System.Windows.Input.MouseEventHandler(this.Canvas_MouseMove);
            
            #line default
            #line hidden
            
            #line 76 "..\..\..\..\Windows\PositionSetupWindow.xaml"
            this.MarkersCanvas.MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.Canvas_MouseLeftButtonUp);
            
            #line default
            #line hidden
            return;
            case 7:
            this.Point1Text = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.Point2Text = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.Point3Text = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.Point4Text = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.ResetButton = ((System.Windows.Controls.Button)(target));
            
            #line 117 "..\..\..\..\Windows\PositionSetupWindow.xaml"
            this.ResetButton.Click += new System.Windows.RoutedEventHandler(this.Reset_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.ConfirmButton = ((System.Windows.Controls.Button)(target));
            
            #line 125 "..\..\..\..\Windows\PositionSetupWindow.xaml"
            this.ConfirmButton.Click += new System.Windows.RoutedEventHandler(this.Confirm_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 133 "..\..\..\..\Windows\PositionSetupWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Cancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

