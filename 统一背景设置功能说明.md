# 统一背景设置功能使用说明

## 功能概述

新增的**统一背景设置功能**可以让您为所有批次使用相同的背景图和定位设置，大大提高批量处理的效率。

### 🎯 主要优势

1. **一次设置，全部应用**：只需设置一次背景图和定位点，自动应用到所有批次
2. **提高效率**：避免为每个批次重复设置相同的背景和定位
3. **保持一致性**：确保所有批次使用完全相同的背景和定位设置
4. **灵活切换**：可以随时启用或禁用统一设置模式

## 使用方法

### 方式一：启用统一设置后批量配置

1. **扫描文件夹**
   - 选择源文件夹并点击"扫描文件夹"
   - 程序会自动识别所有批次

2. **启用统一背景设置**
   - 勾选"所有批次使用统一背景和定位设置"复选框
   - "设置统一背景"按钮会变为可用状态

3. **设置统一背景**
   - 点击"设置统一背景"按钮
   - 选择要使用的背景图片
   - 在弹出的定位窗口中设置定位区域（推荐使用区域模式）
   - 点击"确定"完成设置

4. **自动应用**
   - 程序会自动将背景图和定位设置应用到所有批次
   - 显示"统一背景设置完成！已应用到所有批次"的提示

### 方式二：先设置单个批次再启用统一设置

1. **设置第一个批次**
   - 为任意一个批次选择背景图
   - 设置该批次的定位点

2. **启用统一设置**
   - 勾选"所有批次使用统一背景和定位设置"复选框

3. **应用到其他批次**
   - 为其他批次点击"设置定位点"按钮
   - 程序会自动应用第一个批次的设置

## 界面说明

### 新增控件

- **复选框**："所有批次使用统一背景和定位设置"
  - 未勾选：每个批次独立设置背景和定位
  - 已勾选：所有批次使用相同的背景和定位设置

- **按钮**："设置统一背景"
  - 仅在勾选统一设置时可用
  - 点击后可以选择背景图并设置定位点

### 行为变化

#### 启用统一设置时：
- **选择背景按钮**：点击时会提示使用"设置统一背景"按钮
- **设置定位按钮**：自动应用统一的背景和定位设置
- **状态显示**：显示"已应用统一设置"等提示信息

#### 禁用统一设置时：
- **恢复原有行为**：每个批次可以独立设置背景和定位

## 使用场景

### 适合使用统一设置的情况：
- ✅ 所有内容图片都要合成到同一种背景上
- ✅ 所有图片的放置位置和大小要求一致
- ✅ 批量处理大量相似的图片
- ✅ 需要保持所有输出图片的一致性

### 不适合使用统一设置的情况：
- ❌ 不同批次需要使用不同的背景图
- ❌ 不同批次的定位要求不同
- ❌ 需要为特定批次做个性化调整

## 操作流程图

```
开始
  ↓
选择源文件夹 → 扫描文件夹
  ↓
勾选"统一背景设置"
  ↓
点击"设置统一背景"
  ↓
选择背景图片
  ↓
设置定位区域（区域模式推荐）
  ↓
确认设置
  ↓
自动应用到所有批次
  ↓
开始处理
```

## 注意事项

### 设置顺序
1. **先扫描文件夹**：确保已经识别了所有批次
2. **再启用统一设置**：避免设置丢失
3. **最后设置背景**：确保设置能正确应用

### 设置修改
- **修改统一设置**：重新点击"设置统一背景"即可
- **禁用统一设置**：取消勾选复选框，恢复独立设置模式
- **部分修改**：如需为个别批次使用不同设置，请禁用统一模式

### 状态提示
程序会在状态栏显示相关信息：
- "已将统一设置应用到 X 个批次"
- "已设置定位点并应用到所有批次"
- "已应用统一设置"

## 与智能区域定位的结合

统一背景设置功能完美支持新的智能区域定位功能：

1. **推荐使用区域模式**：拖拽选择目标区域，自动保持比例
2. **一次设置，处处适用**：区域设置会自动应用到所有批次
3. **真实合成效果**：结合智能缩放，确保所有图片都有一致的真实效果

## 故障排除

### 常见问题

**Q: 勾选统一设置后，为什么"设置统一背景"按钮是灰色的？**
A: 需要先扫描文件夹，确保有批次数据后才能设置统一背景。

**Q: 设置了统一背景，但某个批次显示的背景不对？**
A: 检查是否在设置统一背景后又手动修改了该批次的背景，建议重新应用统一设置。

**Q: 如何取消统一设置？**
A: 取消勾选"所有批次使用统一背景和定位设置"复选框即可。

**Q: 统一设置是否会保存？**
A: 当前版本的统一设置在程序重启后需要重新配置，建议在一次会话中完成所有设置和处理。

---

**提示**：统一背景设置功能特别适合批量处理相同类型的图片，可以显著提高工作效率并确保输出的一致性。
