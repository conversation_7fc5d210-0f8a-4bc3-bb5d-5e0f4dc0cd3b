using System;
using System.IO;
using System.Linq;
using OpenCvSharp;
using RealPicComposer.Models;
using RealPicComposer.Utils;

namespace RealPicComposer.Services
{
    /// <summary>
    /// 图像处理服务，负责透视变换和图像合成
    /// </summary>
    public class ImageProcessingService
    {
        private readonly FolderScanService _folderScanService;

        public ImageProcessingService()
        {
            _folderScanService = new FolderScanService();
        }

        /// <summary>
        /// 处理单个批次
        /// </summary>
        /// <param name="batch">批次信息</param>
        /// <param name="outputFolder">输出文件夹</param>
        /// <param name="useOutputFolder">是否使用输出文件夹</param>
        /// <param name="progressCallback">进度回调</param>
        public void ProcessBatch(BatchItem batch, string outputFolder, bool useOutputFolder, Action<double>? progressCallback = null)
        {
            if (batch?.DestinationPoints == null || batch.DestinationPoints.Length != 4)
            {
                throw new ArgumentException("批次的定位点信息不完整");
            }

            if (!Directory.Exists(batch.BatchPath))
            {
                throw new DirectoryNotFoundException($"批次文件夹不存在: {batch.BatchPath}");
            }

            if (!File.Exists(batch.BackgroundPath))
            {
                throw new FileNotFoundException($"背景图文件不存在: {batch.BackgroundPath}");
            }

            // 获取批次中的所有图片文件
            var imageFiles = _folderScanService.GetImageFiles(batch.BatchPath);
            
            if (!imageFiles.Any())
            {
                throw new InvalidOperationException($"批次文件夹中没有找到图片文件: {batch.BatchPath}");
            }

            // 确定实际的输出路径
            string actualOutputPath;
            if (useOutputFolder)
            {
                // 使用输出文件夹，按批次文件夹名字创建子文件夹
                var batchFolderName = Path.GetFileName(batch.BatchPath);
                actualOutputPath = Path.Combine(outputFolder, batchFolderName);
                Directory.CreateDirectory(actualOutputPath);
            }
            else
            {
                // 直接在源文件路径下保存
                actualOutputPath = batch.BatchPath;
            }

            // 加载背景图
            using var backgroundMat = Cv2.ImRead(batch.BackgroundPath);
            if (backgroundMat.Empty())
            {
                throw new InvalidOperationException($"无法加载背景图: {batch.BackgroundPath}");
            }

            // 处理每个图片文件
            for (int i = 0; i < imageFiles.Count; i++)
            {
                try
                {
                    ProcessSingleImage(imageFiles[i], backgroundMat, batch.DestinationPoints, actualOutputPath, useOutputFolder);

                    // 更新进度
                    double progress = (double)(i + 1) / imageFiles.Count * 100;
                    progressCallback?.Invoke(progress);
                }
                catch (Exception ex)
                {
                    // 记录错误但继续处理其他文件
                    Console.WriteLine($"处理图片 {imageFiles[i]} 时发生错误: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 处理单个图片文件
        /// </summary>
        /// <param name="imagePath">图片路径</param>
        /// <param name="backgroundMat">背景图Mat对象</param>
        /// <param name="destinationPoints">目标四角点</param>
        /// <param name="outputPath">输出路径</param>
        /// <param name="useOutputFolder">是否使用输出文件夹</param>
        private void ProcessSingleImage(string imagePath, Mat backgroundMat, Point2f[] destinationPoints, string outputPath, bool useOutputFolder)
        {
            using var contentMat = Cv2.ImRead(imagePath);
            if (contentMat.Empty())
            {
                throw new InvalidOperationException($"无法加载内容图: {imagePath}");
            }

            // 创建背景图的副本用于合成
            using var resultMat = backgroundMat.Clone();

            // 执行透视变换和合成
            ApplyPerspectiveTransform(contentMat, resultMat, destinationPoints);

            // 生成输出文件名和路径
            var originalFileName = Path.GetFileNameWithoutExtension(imagePath);
            var extension = Path.GetExtension(imagePath);
            string finalOutputPath;

            if (useOutputFolder)
            {
                // 使用输出文件夹时，保持原文件名
                var outputFileName = $"{originalFileName}{extension}";
                finalOutputPath = Path.Combine(outputPath, outputFileName);
            }
            else
            {
                // 不使用输出文件夹时，在源路径下使用"合成真实-"前缀
                var outputFileName = $"合成真实-{originalFileName}{extension}";
                finalOutputPath = Path.Combine(outputPath, outputFileName);
            }

            // 保存合成结果
            Cv2.ImWrite(finalOutputPath, resultMat);
        }

        /// <summary>
        /// 应用透视变换并合成图像
        /// </summary>
        /// <param name="contentMat">内容图</param>
        /// <param name="backgroundMat">背景图（将被修改）</param>
        /// <param name="destinationPoints">目标四角点</param>
        private void ApplyPerspectiveTransform(Mat contentMat, Mat backgroundMat, Point2f[] destinationPoints)
        {
            // 验证目标点是否在背景图范围内
            foreach (var point in destinationPoints)
            {
                if (point.X < 0 || point.X >= backgroundMat.Width ||
                    point.Y < 0 || point.Y >= backgroundMat.Height)
                {
                    Console.WriteLine($"警告：定位点 ({point.X}, {point.Y}) 超出背景图范围 ({backgroundMat.Width}x{backgroundMat.Height})");
                }
            }

            // 定义内容图的四个角点（按左上、右上、右下、左下的顺序）
            var sourcePoints = new Point2f[]
            {
                new Point2f(0, 0),                                    // 左上
                new Point2f(contentMat.Width - 1, 0),                 // 右上
                new Point2f(contentMat.Width - 1, contentMat.Height - 1), // 右下
                new Point2f(0, contentMat.Height - 1)                 // 左下
            };

            // 输出调试信息
            Console.WriteLine("源图像角点:");
            for (int i = 0; i < sourcePoints.Length; i++)
            {
                Console.WriteLine($"  {i}: ({sourcePoints[i].X}, {sourcePoints[i].Y})");
            }
            Console.WriteLine("目标角点:");
            for (int i = 0; i < destinationPoints.Length; i++)
            {
                Console.WriteLine($"  {i}: ({destinationPoints[i].X}, {destinationPoints[i].Y})");
            }

            // 计算透视变换矩阵
            using var transformMatrix = Cv2.GetPerspectiveTransform(sourcePoints, destinationPoints);

            // 应用透视变换
            using var transformedMat = new Mat();
            Cv2.WarpPerspective(contentMat, transformedMat, transformMatrix, backgroundMat.Size());

            // 创建掩码以实现更好的混合效果
            using var mask = new Mat();
            using var grayTransformed = new Mat();
            Cv2.CvtColor(transformedMat, grayTransformed, ColorConversionCodes.BGR2GRAY);
            Cv2.Threshold(grayTransformed, mask, 1, 255, ThresholdTypes.Binary);

            // 使用掩码将变换后的图像合成到背景上
            transformedMat.CopyTo(backgroundMat, mask);
        }

        /// <summary>
        /// 验证定位点是否有效
        /// </summary>
        /// <param name="points">四角点数组</param>
        /// <param name="imageSize">图像尺寸</param>
        /// <returns>是否有效</returns>
        public bool ValidateDestinationPoints(Point2f[] points, Size imageSize)
        {
            if (points == null || points.Length != 4)
                return false;

            // 检查所有点是否在图像范围内
            foreach (var point in points)
            {
                if (point.X < 0 || point.X >= imageSize.Width ||
                    point.Y < 0 || point.Y >= imageSize.Height)
                {
                    return false;
                }
            }

            // 检查四边形是否为凸多边形（简单检查）
            return IsConvexQuadrilateral(points);
        }

        /// <summary>
        /// 检查四个点是否构成凸四边形
        /// </summary>
        /// <param name="points">四个点</param>
        /// <returns>是否为凸四边形</returns>
        private bool IsConvexQuadrilateral(Point2f[] points)
        {
            if (points.Length != 4) return false;

            // 计算叉积来判断是否为凸多边形
            for (int i = 0; i < 4; i++)
            {
                var p1 = points[i];
                var p2 = points[(i + 1) % 4];
                var p3 = points[(i + 2) % 4];

                var cross = (p2.X - p1.X) * (p3.Y - p1.Y) - (p2.Y - p1.Y) * (p3.X - p1.X);
                
                // 如果有不同符号的叉积，说明不是凸多边形
                if (i == 0)
                {
                    var sign = Math.Sign(cross);
                    if (sign == 0) return false; // 三点共线
                }
                else
                {
                    var currentSign = Math.Sign(cross);
                    if (currentSign == 0) return false; // 三点共线
                    
                    // 这里简化处理，实际应该检查所有叉积的符号一致性
                }
            }

            return true;
        }

        /// <summary>
        /// 获取图像尺寸信息
        /// </summary>
        /// <param name="imagePath">图像路径</param>
        /// <returns>图像尺寸</returns>
        public Size GetImageSize(string imagePath)
        {
            if (!File.Exists(imagePath))
                return new Size(0, 0);

            try
            {
                using var mat = Cv2.ImRead(imagePath);
                return mat.Size();
            }
            catch
            {
                return new Size(0, 0);
            }
        }

        /// <summary>
        /// 检查图像文件是否有效
        /// </summary>
        /// <param name="imagePath">图像路径</param>
        /// <returns>是否有效</returns>
        public bool IsValidImageFile(string imagePath)
        {
            if (!File.Exists(imagePath))
                return false;

            try
            {
                using var mat = Cv2.ImRead(imagePath);
                return !mat.Empty();
            }
            catch
            {
                return false;
            }
        }
    }
}
